using System;
using System.Collections.Generic;
using DG.Tweening;
using FairyGUI;
using TMPro;
using UnityEngine;
using UnityEngine.U2D;

public class Tile3D : MonoBehaviour
{
    private static Dictionary<int, Material> _materialCache = new Dictionary<int, Material>();
    private readonly int SHADER_ID_BaseColor = Shader.PropertyToID("_BaseColor");
    public const int TYPE_EMPTY = 0;
    public const int TYPE_BLOCK = 1;
    public const int TYPE_BLOCK_END = 99;
    public const int TYPE_QUESTION = 100;
    public const int TYPE_FREEZE1 = 201;
    public const int TYPE_FREEZE2 = 202;
    public const int TYPE_FREEZE3 = 203;
    public const int TYPE_CHAIN = 300;
    public const int TYPE_WOOD = 400;
    public const int TYPE_STONE = 500;
    public const int TYPE_FIRECRACKER = 600;

    public GameObject cube;
    // public TextMeshPro txt;
    public GameObject chainGo;
    // public GameObject topIcon;
    public SpriteRenderer topIcon;

    public Action OnClick;
    private Color selectedColor = Color.green;
    private Color promptColor = Color.cyan;

    [NonSerialized] public bool isSelected;
    [NonSerialized] public bool isPrompt;
    [NonSerialized] public bool isShuffling;
    [NonSerialized] public int freezeLayer;
    [NonSerialized] public bool isYoyo;
    public BoardCell Block
    {
        get { return _block; }
    }

    private Renderer _renderer;
    private MaterialPropertyBlock _propBlock;
    private Color _originalCubeColor;
    private Color _darkColor;

    private BoardCell _block;
    private Vector3 _initScale;
    private const float POP_HEIGHT = 0.2f;
    public int type;

    private PoolItem _selectedEffect;
    private PoolItem _firecrackerEffect;
    private void Awake()
    {
        _renderer = cube.GetComponent<MeshRenderer>();
        _propBlock = new MaterialPropertyBlock();
        _renderer.GetPropertyBlock(_propBlock);
        _originalCubeColor = _renderer.sharedMaterial.color;
        _darkColor = _originalCubeColor;
        _initScale = topIcon.transform.localScale;
    }

    private void SetCubeColor(Color color, int sortingOrder)
    {
        _renderer.GetPropertyBlock(_propBlock);
        _propBlock.SetColor(SHADER_ID_BaseColor, color);
        _renderer.SetPropertyBlock(_propBlock);
        _renderer.sortingOrder = sortingOrder;
    }

    public void SetData(BoardCell block, BattleScene battleScene)
    {
        _block = block;

        selectedColor = battleScene.selectedColor;
        promptColor = battleScene.promptColor;

        _renderer.sharedMaterial.mainTexture = BattleResources.Inst.GetBlockTexture(TYPE_BLOCK);
        SetCubeColor(_originalCubeColor, 0);
        SetIcon(block.type);
        if (IsQuestion)
        {
            cube.transform.rotation = Quaternion.Euler(0, 0, 180);
        }
        else if (IsFirecracker)
        {
            _originalCubeColor = new Color(0.91f, 0.435f, 0.435f);
            SetCubeColor(_originalCubeColor, GetCurrentCubeOrder());
            YoyoIcon(1.2f, 0.9f);
            ShowFirecrackerEffect();
        }
    }

    private void SetIcon(int type)
    {
        this.type = type;
        if (type == TYPE_EMPTY)
        {
            gameObject.SetActive(false);
            return;
        }

        chainGo.SetActive(IsChain);

        if (type < TYPE_BLOCK_END || IsChain)
        {
            var iconIndex = IsChain ? type - TYPE_CHAIN : type;
            topIcon.gameObject.SetActive(true);
            topIcon.sprite = BattleResources.Inst.GetThemeIcon(iconIndex - 1);
        }
        else if (IsQuestion)
        {
            topIcon.gameObject.SetActive(false);
        }
        else if (IsFirecracker)
        {
            topIcon.sprite = BattleResources.Inst.firecracker;
            topIcon.sortingOrder = 2;
        }
        else
        {
            if (!_materialCache.TryGetValue(type, out var mat))
            {
                mat = new Material(_renderer.sharedMaterial);
                mat.mainTexture = BattleResources.Inst.GetBlockTexture(type);
                _materialCache[type] = mat;
            }
            _renderer.sharedMaterial = mat;
            topIcon.gameObject.SetActive(false);
        }
    }

    public bool IsQuestion
    {
        get
        {
            return _block.type > TYPE_QUESTION && _block.type < TYPE_QUESTION + 100;
        }
    }

    public bool IsChain
    {
        get
        {
            return _block.type > TYPE_CHAIN && _block.type < TYPE_CHAIN + 100;
        }
    }
    public bool IsWood
    {
        get
        {
            return _block.type == TYPE_WOOD;
        }
    }
    public bool IsStone
    {
        get
        {
            return _block.type == TYPE_STONE;
        }
    }

    public bool IsFirecracker
    {
        get
        {
            return _block.type == TYPE_FIRECRACKER;
        }
    }

    public bool IsFreeze
    {
        get
        {
            return _block.type >= TYPE_FREEZE1 && _block.type <= TYPE_FREEZE3;
        }
    }

    /// <summary>
    /// 可被点击选中
    /// </summary>
    /// <value></value>
    public bool CanSelect
    {
        get
        {
            return _block.type > TYPE_EMPTY && _block.type < TYPE_BLOCK_END || IsFirecracker;
        }
    }

    /// <summary>
    /// 可被洗牌
    /// </summary>
    /// <value></value>
    public bool CanShuffle
    {
        get
        {
            return _block.type > TYPE_EMPTY && _block.type < TYPE_BLOCK_END || IsFirecracker;
        }
    }

    /// <summary>
    /// 可移动（被重力方向影响）
    /// </summary>
    /// <value></value>
    public bool CanMove
    {
        get
        {
            return _block.type != TYPE_STONE && _block.type != TYPE_FREEZE1 && _block.type != TYPE_FREEZE2 && _block.type != TYPE_FREEZE3;
        }
    }

    /// <summary>
    /// 可连线
    /// </summary>
    /// <value></value>
    public bool CanMatch
    {
        get
        {
            return _block.type >= TYPE_BLOCK && _block.type <= TYPE_BLOCK_END || IsQuestion || IsChain || IsFirecracker;
        }
    }

    public bool TryOpenTile()
    {
        if (IsQuestion)
        {
            _block.type -= TYPE_QUESTION;
            SetIcon(_block.type);
            cube.transform.DORotate(new Vector3(0, 0, 180), 0.3f, RotateMode.WorldAxisAdd);
            return true;
        }
        else if (IsChain)
        {
            _block.type -= TYPE_CHAIN;
            SetIcon(_block.type);
            return true;
        }
        else if (IsFreeze)
        {
            switch (_block.type)
            {
                case TYPE_FREEZE3:
                    _block.type = TYPE_FREEZE2;
                    break;
                case TYPE_FREEZE2:
                    _block.type = TYPE_FREEZE1;
                    break;
                case TYPE_FREEZE1:
                    _block.type = TYPE_EMPTY;
                    break;
            }
            SetIcon(_block.type);
            return true;
        }
        return false;
    }

    public bool RemoveSpecialTile()
    {
        var needRemoveTile = false;
        if (IsQuestion)
        {
            _block.type -= TYPE_QUESTION;
            SetIcon(_block.type);
            cube.transform.DORotate(new Vector3(0, 0, 180), 0.3f, RotateMode.WorldAxisAdd);
        }
        else if (IsChain)
        {
            _block.type -= TYPE_CHAIN;
            SetIcon(_block.type);
        }
        else if (IsFirecracker)
        {
            //不用处理
        }
        else
        {
            _block.type = TYPE_EMPTY;
            SetIcon(_block.type);
            needRemoveTile = true;
        }
        return needRemoveTile;
    }


    public void StartShuffle()
    {
        isShuffling = true;
        isSelected = false;
        transform.DOKill(true);
        transform.rotation = Quaternion.identity;
        transform.localScale = Vector3.one;
        SetCubeColor(GetCurrentMatColor(), 0);
        StopYoyoIcon();

        // 隐藏选中特效
        HideSelectedEffect();
    }
    public void EndShuffle()
    {
        isShuffling = false;
    }

    public void OnMatch()
    {
        transform.DOScale(0.1f, 0.1f);
    }

    public void OnUnmatch()
    {
        transform.rotation = Quaternion.Euler(0, -15, 0);
        transform.DORotate(new Vector3(0, 15, 0), 0.08f)
            .SetLoops(3, LoopType.Yoyo)
            .OnComplete(() => transform.rotation = Quaternion.identity);
    }


    private bool inDoubleClick = false;
    public void OnDoubleClick(float duration = 0.1f)
    {
        if (inDoubleClick || isShuffling)
            return;
        inDoubleClick = true;

        cube.transform.localPosition = new Vector3(0, POP_HEIGHT, 0);

        transform.rotation = Quaternion.Euler(0, -15, 0);
        SetCubeColor(selectedColor, 1);
        transform.DORotate(new Vector3(0, 15, 0), duration)
            .SetLoops(6, LoopType.Yoyo)
            .OnComplete(() =>
            {
                inDoubleClick = false;
                transform.rotation = Quaternion.identity;

                var y = isSelected ? POP_HEIGHT : 0;
                cube.transform.localPosition = new Vector3(0, y, 0);
                SetCubeColor(GetCurrentMatColor(), GetCurrentCubeOrder());
                if (_selectedEffect != null)
                {
                    _selectedEffect.transform.rotation = Quaternion.identity;
                }
            });
    }

    private Color GetCurrentMatColor()
    {
        if (isSelected)
        {
            return selectedColor;
        }
        else if (isPrompt)
        {
            return promptColor;
        }
        // else if (!topIcon.gameObject.activeInHierarchy)
        // {
        //     return Color.white;
        // }

        return _originalCubeColor;
    }

    private int GetCurrentCubeOrder()
    {
        if (isPrompt)
        {
            return 2;
        }
        if (isSelected)
        {
            return 1;
        }
        if (IsFirecracker)
        {
            return 3;
        }
        return 0;
    }

    private void YoyoIcon(float scale = 1.1f, float duration = 0.6f)
    {
        if (isYoyo)
            return;
        isYoyo = true;
        topIcon.transform.DOScale(_initScale * scale, duration).SetLoops(-1, LoopType.Yoyo);
    }

    private void StopYoyoIcon()
    {
        if (isSelected || isPrompt || IsFirecracker)
            return;
        isYoyo = false;
        topIcon.transform.DOKill();
        topIcon.transform.localScale = _initScale;
    }

    public void OnPrompt()
    {
        isPrompt = true;
        SetCubeColor(promptColor, GetCurrentCubeOrder());
        YoyoIcon();
    }

    public void OnUnprompt()
    {
        isPrompt = false;
        SetCubeColor(GetCurrentMatColor(), GetCurrentCubeOrder());
        StopYoyoIcon();
    }

    public void OnSelect()
    {
        if (isSelected)
            return;
        isSelected = true;
        SetCubeColor(selectedColor, GetCurrentCubeOrder());

        cube.transform.localPosition = new Vector3(0, POP_HEIGHT, 0);
        YoyoIcon();

        // 显示选中特效
        ShowSelectedEffect();
    }

    public void OnDeselect()
    {
        if (!isSelected)
            return;
        isSelected = false;
        SetCubeColor(GetCurrentMatColor(), GetCurrentCubeOrder());

        cube.transform.localPosition = Vector3.zero;

        StopYoyoIcon();

        // 隐藏选中特效
        HideSelectedEffect();
    }

    public void OnRemove()
    {
        // 清理爆竹特效
        HideFirecrackerEffect();
        gameObject.SetActive(false);
    }

    /// <summary>
    /// 震动效果 - 向外移动一点然后回来
    /// </summary>
    /// <param name="direction">震动方向</param>
    /// <param name="intensity">震动强度</param>
    public void PlayShakeEffect(Vector3 direction, float intensity = 0.1f)
    {
        if (isShuffling || !gameObject.activeInHierarchy)
            return;

        // 记录原始位置
        Vector3 originalPos = transform.position;

        // 向外移动的目标位置
        Vector3 shakePos = originalPos + direction.normalized * intensity;

        // 使用DOTween创建震动动画序列
        var sequence = DOTween.Sequence();
        sequence.Append(transform.DOMove(shakePos, 0.08f).SetEase(Ease.OutQuad))
                .Append(transform.DOMove(originalPos, 0.12f).SetEase(Ease.InOutQuad));
    }

    private void ShowSelectedEffect()
    {
        if (_selectedEffect != null)
            return;

        _selectedEffect = BattleResources.Inst.GetPoolItem(PoolNames.Pool_EffectTileSelected);
        if (_selectedEffect != null)
        {
            _selectedEffect.transform.position = transform.position;
            _selectedEffect.transform.rotation = Quaternion.identity;
            _selectedEffect.transform.SetParent(transform);
        }
    }

    private void HideSelectedEffect()
    {
        if (_selectedEffect != null)
        {
            _selectedEffect.Release();
            _selectedEffect = null;
        }
    }

    private void ShowFirecrackerEffect()
    {
        if (_firecrackerEffect != null)
            return;

        _firecrackerEffect = BattleResources.Inst.GetPoolItem(PoolNames.Pool_EffectShinePink);
        if (_firecrackerEffect != null)
        {
            _firecrackerEffect.transform.position = transform.position;
            _firecrackerEffect.transform.rotation = Quaternion.identity;
            _firecrackerEffect.transform.SetParent(transform);
        }
    }

    private void HideFirecrackerEffect()
    {
        if (_firecrackerEffect != null)
        {
            Destroy(_firecrackerEffect);
            _firecrackerEffect = null;
        }
    }

    private void OnDestroy()
    {
        if (topIcon && topIcon.transform)
        {
            topIcon.transform.DOKill();
        }

        // 清理选中特效
        HideSelectedEffect();

        // 清理爆竹特效
        HideFirecrackerEffect();
    }

    public void SetDark()
    {
        // SetColor(_darkColor);
    }

    public void SetBright()
    {
        // SetColor(_originalCubeColor);
    }

    public static void ClearMaterialCache()
    {
        foreach (var mat in _materialCache.Values)
        {
            Destroy(mat);
        }
        _materialCache.Clear();
    }
}
