using System;
using System.Collections.Generic;
using FairyGUI;
using UnityEngine;

public class GmTool
{
    public Action OnPassGate;
    private GComponent contentPane;

    private bool _isShow;
    internal bool IsShow
    {
        get => _isShow;
    }

    private GButton btnAdSwitch;
    private GButton btnChanel;
    internal void Show(GComponent parent, Vector2 pos)
    {
        _isShow = true;
        if (contentPane == null)
        {
            contentPane = UIPackage.CreateObject("Battle", "TestPanel").asCom;
            contentPane.xy = pos;
            btnAdSwitch = contentPane.GetChild("btnAdSwitch").asButton;
            btnChanel = contentPane.GetChild("btnChannel").asButton;
        }
        UpdateAdSwitch();
        UpdateChannel();
        parent.AddChild(contentPane);

        contentPane.onClick.Add(OnClick);
    }

    private void OnClick(EventContext context)
    {
        var target = context.initiator as DisplayObject;
        if (target == null || target.gOwner == null)
            return;

        var targetName = target.gOwner.name;
        switch (targetName)
        {
            case "btnPreGate":
                GoPreGate();
                break;
            case "btnNextGate":
                GoNextGate();
                break;
            case "btnPassGate":
                OnPassGate?.Invoke();
                break;
            case "btnAdSwitch":
                Session.jumpVideoAd = !Session.jumpVideoAd;
                UpdateAdSwitch();
                break;
            case "btnChannel":
                ChangeChannel();
                break;
        }
    }
    private void UpdateAdSwitch()
    {
        btnAdSwitch.title = Session.jumpVideoAd ? LangUtil.GetText("txtJumpVideoAdOff") : LangUtil.GetText("txtJumpVideoAdOn");
    }

    private void GoPreGate()
    {
        if (GameGlobal.EnterLevel > 1)
        {
            GameGlobal.Level = GameGlobal.EnterLevel = GameGlobal.EnterLevel - 1;
            new CmdRestartBattle().Execute();
        }
    }

    private void GoNextGate()
    {
        GameGlobal.Level = GameGlobal.EnterLevel = GameGlobal.EnterLevel + 1;
        new CmdRestartBattle().Execute();
    }

    private void UpdateChannel()
    {
        btnChanel.text = LangUtil.GetText("txtAbtest", GameGlobal.Channel);
    }

    private void ChangeChannel()
    {
        var channel = new List<string> { "A", "B", "C" };
        var curIndex = channel.IndexOf(GameGlobal.Channel);
        curIndex++;
        if (curIndex >= channel.Count)
            curIndex = 0;
        GameGlobal.Channel = channel[curIndex];
        UpdateChannel();

        ConfigLoader.Inst.LoadConfigs(() =>
        {
            new CmdRestartBattle().Execute();
        });
    }

    internal void Hide()
    {
        if (contentPane == null)
            return;
        _isShow = false;
        contentPane.RemoveFromParent();
    }
}